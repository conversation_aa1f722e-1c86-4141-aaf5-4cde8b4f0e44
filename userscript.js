// ==UserScript==
// @name         Brave Enhancement
// @version      3.8.0
// @match        https://gota.io/*
// @require      https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @connect      localhost
// @run-at       document-start
// ==/UserScript==

(function() {
    if (window.braveEnhanced) return;
    window.braveEnhanced = true;

    // Se estiver na URL /web/, injeta o index.html do localhost
    if (window.location.pathname === '/web/') {
        injectLocalIndex();
        return;
    }

    // Código original para outras páginas
    const scriptUrl = 'http://localhost:5500/brave.js';
    const cssUrl = 'http://localhost:5500/brave.css';
    const scriptBlocker = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
            for (const node of mutation.addedNodes) {
                if (node.tagName === 'SCRIPT' && node.src && node.src.includes('gota.js')) {
                    node.type = 'text/javascript';
                    node.textContent = '';
                    node.removeAttribute('src');
                }
                if (node.tagName === 'LINK' && node.href && node.href.includes('style.css')) {
                    node.remove();
                }

                if (node.tagName === 'BODY') {
                    scriptBlocker.disconnect();
                    injectCustomScripts();
                }
            }
        }
    });
    scriptBlocker.observe(document.documentElement, { childList: true, subtree: true });

    function injectCustomScripts() {
        GM_xmlhttpRequest({
            method: "GET",
            url: cssUrl,
            onload: (response) => {
                if (response.status === 200) GM_addStyle(response.responseText);
            }
        });
        const versionScript = document.createElement('script');
        versionScript.textContent = 'var version = "3.6.5";';
        (document.head || document.documentElement).appendChild(versionScript);
        GM_xmlhttpRequest({
            method: "GET",
            url: scriptUrl,
            onload: (response) => {
                if (response.status === 200) {
                    const mainScript = document.createElement('script');
                    mainScript.textContent = response.responseText;
                    document.body.appendChild(mainScript);
                }
            }
        });
    }

    function injectLocalIndex() {
        // Para a página original de carregar
        document.addEventListener('DOMContentLoaded', function(e) {
            e.stopPropagation();
        }, true);

        // Busca o index.html do localhost
        GM_xmlhttpRequest({
            method: "GET",
            url: 'http://localhost:5500/index.html',
            onload: (response) => {
                if (response.status === 200) {
                    // Substitui todo o conteúdo da página
                    document.open();
                    document.write(response.responseText);
                    document.close();

                    // Injeta os scripts customizados após carregar o index
                    setTimeout(() => {
                        injectBraveAssets();
                    }, 1000);
                }
            },
            onerror: () => {
                console.error('Erro ao carregar index.html do localhost:5500');
            }
        });
    }

    function injectBraveAssets() {
        // Injeta o CSS do Brave
        GM_xmlhttpRequest({
            method: "GET",
            url: 'http://localhost:5500/brave.css',
            onload: (response) => {
                if (response.status === 200) {
                    GM_addStyle(response.responseText);
                }
            }
        });

        // Injeta o JS do Brave
        GM_xmlhttpRequest({
            method: "GET",
            url: 'http://localhost:5500/brave.js',
            onload: (response) => {
                if (response.status === 200) {
                    const script = document.createElement('script');
                    script.textContent = response.responseText;
                    document.body.appendChild(script);
                }
            }
        });
    }
})();