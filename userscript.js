// ==UserScript==
// @name         Brave Enhancement
// @version      3.8.0
// @match        https://gota.io/*
// @require      https://ajax.googleapis.com/ajax/libs/jquery/2.2.4/jquery.min.js
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @connect      localhost
// @run-at       document-start
// ==/UserScript==

(function() {
    if (window.braveEnhanced) return;
    window.braveEnhanced = true;

    const scriptUrl = 'http://localhost:5500/brave.js';
    const cssUrl = 'http://localhost:5500/brave.css';
    const indexUrl = 'http://localhost:5500/index.html';

    // Verifica se estamos na URL /web/ para injetar o index.html completo
    if (window.location.pathname === '/web/' || window.location.pathname === '/web') {
        // Para a URL /web/, substitui completamente o conteúdo da página
        GM_xmlhttpRequest({
            method: "GET",
            url: indexUrl,
            onload: (response) => {
                if (response.status === 200) {
                    // Para o carregamento da página original
                    window.stop();

                    // Substitui completamente o HTML da página
                    document.open();
                    document.write(response.responseText);
                    document.close();

                    // Aguarda um pouco para garantir que o DOM foi carregado
                    setTimeout(() => {
                        injectCustomScripts();
                    }, 100);
                }
            }
        });
        return;
    }

    // Comportamento original para outras URLs
    const scriptBlocker = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
            for (const node of mutation.addedNodes) {
                if (node.tagName === 'SCRIPT' && node.src && node.src.includes('gota.js')) {
                    node.type = 'text/javascript';
                    node.textContent = '';
                    node.removeAttribute('src');
                }
                if (node.tagName === 'LINK' && node.href && node.href.includes('style.css')) {
                    node.remove();
                }

                if (node.tagName === 'BODY') {
                    scriptBlocker.disconnect();
                    injectCustomScripts();
                }
            }
        }
    });
    scriptBlocker.observe(document.documentElement, { childList: true, subtree: true });

    function injectCustomScripts() {
        GM_xmlhttpRequest({
            method: "GET",
            url: cssUrl,
            onload: (response) => {
                if (response.status === 200) GM_addStyle(response.responseText);
            }
        });
        const versionScript = document.createElement('script');
        versionScript.textContent = 'var version = "3.6.5";';
        (document.head || document.documentElement).appendChild(versionScript);
        GM_xmlhttpRequest({
            method: "GET",
            url: scriptUrl,
            onload: (response) => {
                if (response.status === 200) {
                    const mainScript = document.createElement('script');
                    mainScript.textContent = response.responseText;
                    document.body.appendChild(mainScript);
                }
            }
        });
    }
})();